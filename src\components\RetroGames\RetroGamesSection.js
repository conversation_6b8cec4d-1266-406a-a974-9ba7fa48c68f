"use client";

import { motion } from 'framer-motion';
import Button from '../Button';
import { useModal } from '../../contexts/ModalContext';

const RetroGamesSection = () => {
  const { openModal } = useModal();

  const handleEnterArcade = () => {
    openModal('arcade');
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen flex items-center justify-center relative z-10"
    >
      {/* Container with natural spacing */}
      <div className="w-3/4 mx-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
          className="flex items-center justify-center"
          style={{ height: '70vh' }}
        >
          <div className="bg-primary rounded-3xl p-12 text-center max-w-2xl shadow-lg">
            <div className="space-y-6">
              <div className="flex items-center justify-center space-x-3 mb-6">
                <span className="text-3xl animate-pulse">🎮</span>
                <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl">
                  Retro Arcade
                </h2>
                <span className="text-3xl animate-pulse">🕹️</span>
              </div>

              <p className="text-secondary text-lg leading-relaxed mb-6">
                Care for a nostalgia break? Check out some classic games recreated with modern web tech for your enjoyment.
              </p>

              <p className="text-secondary/80 text-base mb-8">
                Want to see a more interactive project? Step into the arcade!
              </p>

              <Button
                variant="filled"
                onClick={handleEnterArcade}
              >
                Enter Arcade
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default RetroGamesSection;
