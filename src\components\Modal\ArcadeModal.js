"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from '../RetroGames/GameConsole';
import GameScreen from '../RetroGames/GameScreen';
import Button from '../Button';

const ArcadeModal = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [showGameInfo, setShowGameInfo] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, Snake became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.',
      controls: 'Use WASD or Arrow Keys to move your snake around the screen.'
    },
    {
      id: 'gighunt',
      title: 'Gig Hunt',
      description: 'Hunt software icons to gain knowledge points, then hunt projects to land the gig!',
      color: '#f59e0b', // Orange
      icon: '🎯',
      trivia: 'A creative twist on Duck Hunt! William helps you hunt software knowledge and projects. Built with React and SVG sprites for that authentic retro feel.',
      controls: 'Click on flying PixelPrey to hunt them. Avoid dangerous ones that cost you lives!'
    }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setShowGameInfo(true);
      setIsTransitioning(false);
    }, 300);
  };

  const handleStartGame = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setShowGameInfo(false);
      setIsTransitioning(false);
    }, 300);
  };

  const handleBackToInfo = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setShowGameInfo(true);
      setIsTransitioning(false);
    }, 300);
  };

  const handleBackToMenu = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setShowGameInfo(false);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <div className="w-full h-full bg-background rounded-lg overflow-hidden flex flex-col">
      {/* Arcade Header */}
      <div className="bg-primary p-6 flex-shrink-0">
        <div className="flex items-center justify-center space-x-3">
          <span className="text-3xl animate-pulse">🎮</span>
          <h2 className="font-heading font-extrabold text-secondary text-3xl">
            Retro Arcade
          </h2>
          <span className="text-3xl animate-pulse">🕹️</span>
        </div>
        <p className="text-secondary/80 text-center mt-2">
          Classic games recreated with modern web tech
        </p>
      </div>

      {/* Arcade Content */}
      <div className="p-6 flex-1 overflow-y-auto">
        <AnimatePresence mode="wait">
          {!selectedGame ? (
            // Main Arcade Menu
            <motion.div
              key="arcade-menu"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="min-h-full flex items-center justify-center"
            >
              <GameConsole
                games={games}
                onGameSelect={handleGameSelect}
                isTransitioning={isTransitioning}
              />
            </motion.div>
          ) : showGameInfo ? (
            // Game Info Screen
            <motion.div
              key="game-info"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="min-h-full flex items-center justify-center py-8"
            >
              <div className="max-w-4xl w-full grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                {/* Game Image/Icon */}
                <div className="flex items-center justify-center">
                  <div 
                    className="w-48 h-48 rounded-3xl flex items-center justify-center text-8xl shadow-lg"
                    style={{ backgroundColor: selectedGame.color + '20', border: `3px solid ${selectedGame.color}` }}
                  >
                    {selectedGame.icon}
                  </div>
                </div>

                {/* Game Details */}
                <div className="space-y-6">
                  <h2 className="font-heading font-extrabold text-secondary text-4xl">
                    {selectedGame.title}
                  </h2>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-heading font-bold text-secondary text-lg mb-2">Description</h3>
                      <p className="text-secondary/80">{selectedGame.description}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                      <p className="text-secondary/80">{selectedGame.controls}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                      <p className="text-secondary/80">{selectedGame.trivia}</p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-4 pt-4">
                    <Button
                      variant="outline"
                      onClick={handleBackToMenu}
                    >
                      ← Back
                    </Button>
                    <Button
                      variant="filled"
                      onClick={handleStartGame}
                    >
                      Start Game
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            // Gameplay Screen
            <motion.div
              key="gameplay"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="min-h-full flex items-center justify-center"
            >
              <GameScreen
                game={selectedGame}
                onBack={handleBackToInfo}
                isTransitioning={isTransitioning}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default ArcadeModal;
