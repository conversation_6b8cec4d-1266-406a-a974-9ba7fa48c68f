"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from '../RetroGames/GameConsole';
import GameScreen from '../RetroGames/GameScreen';
import Button from '../Button';
import { useModal } from '../../contexts/ModalContext';

const ArcadeModal = () => {
  const { openModal } = useModal();
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Update modal data when selectedGame changes to trigger modal resize
  useEffect(() => {
    openModal('arcade', { selectedGame });
  }, [selectedGame, openModal]);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, <PERSON> became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.',
      controls: 'Use WASD or Arrow Keys to move your snake around the screen.'
    },
    {
      id: 'gighunt',
      title: 'Gig Hunt',
      description: 'Hunt software icons to gain knowledge points, then hunt projects to land the gig!',
      color: '#f59e0b', // Orange
      icon: '🎯',
      trivia: 'A creative twist on Duck Hunt! William helps you hunt software knowledge and projects. Built with React and SVG sprites for that authentic retro feel.',
      controls: 'Click on flying PixelPrey to hunt them. Avoid dangerous ones that cost you lives!'
    }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);
    }, 300);
  };

  const handleBackToMenu = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <div className="w-full h-full bg-background rounded-lg overflow-hidden">
      <AnimatePresence mode="wait">
        {!selectedGame ? (
          // Game Selection Screen - Title and games grouped together
          <motion.div
            key="game-selection"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            className="h-full overflow-y-auto flex items-center justify-center p-8"
          >
            <div className="text-center">
              {/* Arcade Header - Grouped with games */}
              <div className="mb-8">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <span className="text-3xl animate-pulse">🎮</span>
                  <h2 className="font-heading font-extrabold text-secondary text-3xl">
                    Retro Arcade
                  </h2>
                  <span className="text-3xl animate-pulse">🕹️</span>
                </div>
                <p className="text-secondary/80">
                  Classic games recreated with modern web tech
                </p>
              </div>

              {/* Game Console */}
              <GameConsole
                games={games}
                onGameSelect={handleGameSelect}
                isTransitioning={isTransitioning}
              />
            </div>
          </motion.div>
        ) : (
          // Gameplay Screen - Like project modal layout
          <motion.div
            key="gameplay"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            className="h-full flex flex-col"
          >
            {/* Back Button */}
            <div className="absolute top-6 left-4 z-10">
              <Button
                variant="outline"
                onClick={handleBackToMenu}
                className="text-sm bg-primary/90 backdrop-blur-sm"
              >
                ← Back
              </Button>
            </div>

            {/* Game Screen - Full width like project modal image */}
            <div className="w-full" style={{ aspectRatio: '4/3' }}>
              <GameScreen
                game={selectedGame}
                onBack={handleBackToMenu}
                isTransitioning={isTransitioning}
              />
            </div>

            {/* Game Info Below - Like project modal content */}
            <div className="flex-1 overflow-y-auto p-8">
              <h2 className="font-heading font-extrabold text-secondary text-2xl mb-6 flex items-center">
                {selectedGame.icon} {selectedGame.title}
              </h2>

              <div className="space-y-6">
                <div>
                  <h3 className="font-heading font-bold text-secondary text-lg mb-2">Description</h3>
                  <p className="text-secondary/80">{selectedGame.description}</p>
                </div>

                <div>
                  <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                  <p className="text-secondary/80">{selectedGame.controls}</p>
                </div>

                <div>
                  <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                  <p className="text-secondary/80">{selectedGame.trivia}</p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ArcadeModal;
