"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from '../RetroGames/GameConsole';
import GameScreen from '../RetroGames/GameScreen';
import Button from '../Button';

const ArcadeModal = () => {
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, <PERSON> became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.',
      controls: 'Use WASD or Arrow Keys to move your snake around the screen.'
    },
    {
      id: 'gighunt',
      title: 'Gig Hunt',
      description: 'Hunt software icons to gain knowledge points, then hunt projects to land the gig!',
      color: '#f59e0b', // Orange
      icon: '🎯',
      trivia: 'A creative twist on Duck Hunt! <PERSON> helps you hunt software knowledge and projects. Built with React and SVG sprites for that authentic retro feel.',
      controls: 'Click on flying PixelPrey to hunt them. Avoid dangerous ones that cost you lives!'
    }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);
    }, 300);
  };

  const handleBackToMenu = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <div className="w-full h-full bg-background rounded-lg overflow-hidden flex flex-col">
      <AnimatePresence mode="wait">
        {!selectedGame ? (
          // Game Selection Screen
          <motion.div
            key="game-selection"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            className="flex-1 overflow-y-auto"
          >
            {/* Arcade Header - Only shown during game selection */}
            <div className="bg-primary p-6">
              <div className="flex items-center justify-center space-x-3">
                <span className="text-3xl animate-pulse">🎮</span>
                <h2 className="font-heading font-extrabold text-secondary text-3xl">
                  Retro Arcade
                </h2>
                <span className="text-3xl animate-pulse">🕹️</span>
              </div>
              <p className="text-secondary/80 text-center mt-2">
                Classic games recreated with modern web tech
              </p>
            </div>

            {/* Game Console */}
            <div className="p-6 flex items-center justify-center min-h-[60vh]">
              <GameConsole
                games={games}
                onGameSelect={handleGameSelect}
                isTransitioning={isTransitioning}
              />
            </div>
          </motion.div>
        ) : (
          // Gameplay Screen
          <motion.div
            key="gameplay"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            className="flex-1 flex flex-col overflow-y-auto"
          >
            {/* Back Button */}
            <div className="bg-primary p-4 flex-shrink-0">
              <Button
                variant="outline"
                onClick={handleBackToMenu}
                className="text-sm"
              >
                ← Back to Games
              </Button>
            </div>

            {/* Game Screen - Expanded */}
            <div className="flex-1 p-6 flex items-center justify-center">
              <div className="w-full max-w-5xl">
                <GameScreen
                  game={selectedGame}
                  onBack={handleBackToMenu}
                  isTransitioning={isTransitioning}
                />
              </div>
            </div>

            {/* Game Info Below */}
            <div className="bg-primary p-6 flex-shrink-0">
              <div className="max-w-4xl mx-auto">
                <h2 className="font-heading font-extrabold text-secondary text-2xl mb-4 flex items-center">
                  {selectedGame.icon} {selectedGame.title}
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="font-heading font-bold text-secondary text-lg mb-2">Description</h3>
                    <p className="text-secondary/80 text-sm">{selectedGame.description}</p>
                  </div>

                  <div>
                    <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                    <p className="text-secondary/80 text-sm">{selectedGame.controls}</p>
                  </div>

                  <div>
                    <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                    <p className="text-secondary/80 text-sm">{selectedGame.trivia}</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ArcadeModal;
