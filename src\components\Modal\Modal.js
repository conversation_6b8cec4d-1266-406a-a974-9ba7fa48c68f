"use client";

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useModal } from '../../contexts/ModalContext';
import ModalContent from './ModalContent';

const Modal = () => {
  const { isOpen, closeModal, modalType } = useModal();

  // Handle escape key and scroll prevention
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        closeModal();
      }
    };

    if (isOpen) {
      // Calculate scrollbar width to prevent layout shift
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
      document.body.style.paddingRight = `${scrollbarWidth}px`;
      document.body.classList.add('modal-open');
      document.addEventListener('keydown', handleEscape);
    } else {
      // Restore body scroll when modal is closed - stay exactly where we are
      document.body.style.overflow = '';
      document.body.style.paddingRight = '';
      document.body.classList.remove('modal-open');
      // No position changes - page stays exactly where it is
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, closeModal]);

  // Backdrop click handler
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-[9999] flex items-center justify-center p-4"
          onClick={handleBackdropClick}
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 1 }}
          transition={{ duration: 0 }}
        >
          {/* Backdrop - No animation, instant blur + darken */}
          <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />

          {/* Modal Container */}
          <motion.div
            className={`relative w-full ${modalType === 'arcade' ? 'max-w-[75vw] max-h-[85vh]' : 'max-w-3xl max-h-[90vh]'} bg-primary border border-secondary/20 rounded-lg shadow-2xl overflow-hidden`}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          >
            {/* Close Button */}
            <button
              onClick={closeModal}
              className="absolute top-6 right-4 z-10 w-10 h-10 flex items-center justify-center rounded-full text-secondary/60 hover:text-secondary hover:bg-secondary/10 transition-all duration-200 cursor-pointer"
              aria-label="Close modal"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Scrollable Content */}
            <div className={`modal-content ${modalType === 'arcade' ? 'overflow-hidden max-h-[85vh]' : 'overflow-y-auto max-h-[90vh]'}`}>
              <ModalContent />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
